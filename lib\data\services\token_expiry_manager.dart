import 'dart:async';
import 'package:flutter/material.dart';
import '../../domain/usecases/refresh_token_usecase.dart';
import '../models/user_model.dart';
import '../models/login_response.dart';
import '../../core/utils/preferences.dart';
import '../../core/error/failures.dart';

class TokenExpiryManager {
  final RefreshTokenUseCase refreshTokenUseCase;
  Timer? _refreshTimer;
  static const int _refreshBeforeExpiryMinutes = 5;

  TokenExpiryManager({
    required this.refreshTokenUseCase,
  });

  void startMonitoring() async {
    // Cancel any existing timer
    _refreshTimer?.cancel();

    // Get current user and check expiry
    await _scheduleTokenRefresh();

    // Set up periodic check every minute
    _refreshTimer = Timer.periodic(const Duration(minutes: 1), (_) async {
      await _scheduleTokenRefresh();
    });
  }

  void stopMonitoring() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  Future<void> _scheduleTokenRefresh() async {
    try {
      final User? user = await Preferences.getUser();
      if (user == null) return;

      final DateTime expiryTime = DateTime.fromMillisecondsSinceEpoch(user.exp * 1000);
      final DateTime now = DateTime.now();
      final DateTime refreshTime = expiryTime.subtract(Duration(minutes: _refreshBeforeExpiryMinutes));

      if (now.isAfter(refreshTime) && now.isBefore(expiryTime)) {
        // Time to refresh the token
        final refreshToken = await Preferences.getRefreshToken();
        if (refreshToken == null) return;

        final result = await refreshTokenUseCase(refreshToken);
        result.fold(
          (Failure failure) {
            debugPrint('Token refresh failed: ${failure.message}');
          },
          (LoginResponse response) async {
            await _saveNewTokens(response);
          },
        );
      }
    } catch (e) {
      debugPrint('Error in token refresh schedule: $e');
    }
  }

  Future<void> _saveNewTokens(LoginResponse response) async {
    await Preferences.saveTokens(
      accessToken: response.accessToken,
      refreshToken: response.refreshToken,
    );
    await Preferences.saveUserFromToken(response.accessToken);
  }

  static TokenExpiryManager? _instance;
  
  static void initialize(RefreshTokenUseCase refreshTokenUseCase) {
    _instance?.stopMonitoring();
    _instance = TokenExpiryManager(refreshTokenUseCase: refreshTokenUseCase);
    _instance!.startMonitoring();
  }

  static void dispose() {
    _instance?.stopMonitoring();
    _instance = null;
  }
}
