import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:wesell/main.dart';
import '../../core/utils/preferences.dart';
import '../models/login_response.dart';
import '../datasources/auth_remote_data_source.dart';

class TokenRefreshService {
  final AuthRemoteDataSource remoteDataSource;
  bool _isRefreshing = false;

  TokenRefreshService({required this.remoteDataSource});

  Future<void> handleTokenRefresh(
    DioException error,
    ErrorInterceptorHandler handler,
    Dio dio,
  ) async {
    if (error.response?.statusCode == 401) {
      try {
        final refreshToken = await Preferences.getRefreshToken();
        if (refreshToken == null) {
          _redirectToLogin();
          return handler.reject(error);
        }

        final response = await remoteDataSource.refreshToken(
          refreshToken: refreshToken,
        );
        await _saveNewTokens(response);

        // Retry the original request with new token
        final requestOptions = error.requestOptions;
        final opts = Options(method: requestOptions.method);
        final dio = Dio();

        final token = await Preferences.getAccessToken();
        dio.options.headers['Authorization'] = 'Bearer $token';

        final retryResponse = await dio.request(
          requestOptions.path,
          options: opts,
          data: requestOptions.data,
          queryParameters: requestOptions.queryParameters,
        );

        return handler.resolve(retryResponse);
      } catch (e) {
        _redirectToLogin();
        return handler.reject(error);
      }
    }
    return handler.next(error);
  }

  Future<void> _redirectToLogin()  async {
    await Preferences.clearTokens();
    navigatorKey.currentState?.pushNamedAndRemoveUntil('/login', (route) => false);
  }

  Future<void> _saveNewTokens(LoginResponse response) async {
    await Preferences.saveTokens(
      accessToken: response.accessToken,
      refreshToken: response.refreshToken,
    );
    await Preferences.saveUserFromToken(response.accessToken);
  }
}
